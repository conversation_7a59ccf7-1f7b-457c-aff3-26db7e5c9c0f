'use client'
import LeftPanel from "@/components/athleteHome/LeftPanel";
import RightPanel from "@/components/athleteHome/RightPanel";
import ClientGuard from "@/components/ClientGuard";
import FloatingMessagesWidget from "@/components/messages/FloatingMessagesWidget";
import { usePathname } from "next/navigation";

const AthleteHomeLayout = ({ children }) => {
    const pathname = usePathname();

    if (pathname?.startsWith("/athlete/profile")) {
        return <>{children}</>;
    }

    return (
        <ClientGuard allowedRoles={[2]}>
            <div className="min-h-screen bg-gray-50 space-y-4 w-full p-5">
                <div className="max-w-8xl mx-auto flex flex-col lg:flex-row lg:space-x-6">
                    {/* Left Panel */}
                    {/* <aside className="lg:w-1/4 mb-6 lg:mb-0 order-2 lg:order-1">
                        <LeftPanel />
                    </aside> */}

                    {/* Main Feed */}
                    <main className="lg:w-full order-0 lg:order-2">
                        {children}
                    </main>

                    {/* <FloatingMessagesWidget /> */}

                    {/* Right Panel */}
                    {/* <aside className="lg:w-1/4 mt-6 mb-6 lg:mt-0 order-1 lg:order-3">
                        <div className="sticky top-20">
                            <RightPanel />
                        </div>
                    </aside> */}
                </div>
            </div>
        </ClientGuard>
    )
}
export default AthleteHomeLayout